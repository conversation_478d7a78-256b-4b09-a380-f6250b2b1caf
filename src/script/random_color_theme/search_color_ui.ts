/**
 * 搜索颜色工具UI组件模块
 * 负责创建主题按钮、下拉菜单和模块选择界面
 */

import {
    getAvailableModules,
    getModuleDisplayName
} from './apply_random_theme';
import {
    checkPrivilegedUser,
    moduleStatusModifications,
    markHasModifications
} from './search_color_config';
import { messageError, messageSuccess } from '../prompt_message';

/**
 * 创建生成主题按钮（带模块选择）
 * @returns 创建的按钮元素
 */
export function createGenerateThemeButton(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'apply-theme-button-group';

    // 主按钮
    const mainButton = document.createElement('button');
    mainButton.className = 'add-screenshot-button apply-theme-main-btn';
    mainButton.textContent = '生成随机颜色主题';

    // 下拉按钮
    const dropdownButton = document.createElement('button');
    dropdownButton.className = 'add-screenshot-button apply-theme-dropdown-btn';
    dropdownButton.textContent = '▼';

    // 下拉菜单
    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'apply-theme-dropdown-menu';
    dropdownMenu.style.display = 'none';

    container.appendChild(mainButton);
    container.appendChild(dropdownButton);
    container.appendChild(dropdownMenu);

    // 主按钮点击事件 - 打开模块选择弹窗
    mainButton.addEventListener('click', async () => {
        // 直接触发下拉按钮的点击事件，打开模块选择菜单
        dropdownButton.click();
    });

    // 下拉按钮点击事件 - 显示/隐藏模块选择
    dropdownButton.addEventListener('click', async () => {
        const isVisible = dropdownMenu.style.display !== 'none';

        if (isVisible) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        } else {
            // 获取可用模块并生成选项
            await populateGenerateModuleOptions(dropdownMenu);
            dropdownMenu.style.display = 'block';
            dropdownButton.textContent = '▲';
            dropdownButton.classList.add('active');
        }
    });

    // 点击其他地方时隐藏下拉菜单
    document.addEventListener('click', (e) => {
        if (!container.contains(e.target as Node)) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        }
    });

    return container;
}

/**
 * 创建应用主题按钮
 * @param buttonText 按钮文本
 * @param versionType 版本类型
 * @returns 创建的按钮元素
 */
export function createApplyThemeButton(buttonText: string, versionType: 'color' | 'image' | 'full'): HTMLElement {
    const container = document.createElement('div');
    container.className = 'apply-theme-button-group';

    // 主按钮
    const mainButton = document.createElement('button');
    mainButton.className = 'add-screenshot-button apply-theme-main-btn';
    mainButton.textContent = `应用${buttonText}`;

    // 下拉按钮
    const dropdownButton = document.createElement('button');
    dropdownButton.className = 'add-screenshot-button apply-theme-dropdown-btn version-btn';
    dropdownButton.textContent = '▼';

    // 下拉菜单
    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'apply-theme-dropdown-menu';
    dropdownMenu.style.display = 'none';

    container.appendChild(mainButton);
    container.appendChild(dropdownButton);
    container.appendChild(dropdownMenu);

    // 主按钮点击事件 - 打开下拉菜单
    mainButton.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 检查当前菜单是否可见
        const isVisible = dropdownMenu.style.display !== 'none';

        if (isVisible) {
            // 隐藏菜单
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        } else {
            // 关闭所有其他已打开的下拉菜单
            closeAllOtherDropdowns(dropdownMenu, dropdownButton);

            // 显示当前菜单
            await populateModuleOptions(dropdownMenu, versionType);
            dropdownMenu.style.display = 'block';
            dropdownButton.textContent = '▲';
            dropdownButton.classList.add('active');
        }
    });

    // 下拉按钮点击事件 - 显示/隐藏模块选择
    dropdownButton.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isVisible = dropdownMenu.style.display !== 'none';

        if (isVisible) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        } else {
            // 关闭所有其他已打开的下拉菜单
            closeAllOtherDropdowns(dropdownMenu, dropdownButton);

            // 获取可用模块并生成选项
            await populateModuleOptions(dropdownMenu, versionType);
            dropdownMenu.style.display = 'block';
            dropdownButton.textContent = '▲';
            dropdownButton.classList.add('active');
        }
    });

    // 点击其他地方时隐藏下拉菜单
    document.addEventListener('click', (e) => {
        if (!container.contains(e.target as Node)) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        }
    });

    return container;
}

/**
 * 关闭所有其他已打开的下拉菜单
 */
function closeAllOtherDropdowns(currentDropdownMenu: HTMLElement, currentDropdownButton: HTMLElement): void {
    const allDropdownMenus = document.querySelectorAll('.apply-theme-dropdown-menu');
    const allDropdownButtons = document.querySelectorAll('.apply-theme-dropdown-btn');

    allDropdownMenus.forEach(menu => {
        if (menu !== currentDropdownMenu) {
            (menu as HTMLElement).style.display = 'none';
        }
    });

    allDropdownButtons.forEach(btn => {
        if (btn !== currentDropdownButton) {
            (btn as HTMLElement).textContent = '▼';
            (btn as HTMLElement).classList.remove('active');
        }
    });
}

/**
 * 填充生成模块选择选项
 * @param dropdownMenu 下拉菜单元素
 */
export async function populateGenerateModuleOptions(dropdownMenu: HTMLElement): Promise<void> {
    try {
        // 从全局变量获取模块名称（对象的键）
        const moduleNames = Object.keys(moduleStatusModifications);
        console.log('当前模块列表:', moduleNames);
        console.log('当前模块状态:', moduleStatusModifications);

        if (moduleNames.length === 0) {
            dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">暂无可用模块</div>';
            return;
        }

        // 清空菜单内容
        dropdownMenu.innerHTML = '';

        // 创建固定的顶部控制区域
        const fixedControlsContainer = createFixedControlsContainer();

        // 检查是否为特权用户
        const isPrivilegedUser = await checkPrivilegedUser();

        // 创建按钮组
        const { selectAllItem, generateButton, addButton } = createTopButtons(isPrivilegedUser, moduleNames);

        // 组装按钮到容器
        const topButtonsContainer = fixedControlsContainer.querySelector('.top-buttons-container')!;
        topButtonsContainer.appendChild(selectAllItem);
        topButtonsContainer.appendChild(generateButton);
        if (addButton) {
            topButtonsContainer.appendChild(addButton);
        }

        // 设置按钮事件
        setupButtonEvents(addButton, dropdownMenu);

        // 创建可滚动的模块列表区域
        const scrollableModuleList = createScrollableModuleList(moduleNames);

        // 下拉菜单样式已在CSS中定义

        // 将固定控制区域和可滚动模块列表添加到下拉菜单
        dropdownMenu.appendChild(fixedControlsContainer);
        dropdownMenu.appendChild(scrollableModuleList);

        // 添加事件监听 - 延迟导入以避免循环依赖
        import('./module_config_manager').then(({ setupGenerateModuleEvents }) => {
            setupGenerateModuleEvents(dropdownMenu);
        });

    } catch (error) {
        console.error('填充生成模块选项失败:', error);
        dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">加载模块失败</div>';
    }
}

/**
 * 创建固定控制区域容器
 */
function createFixedControlsContainer(): HTMLElement {
    const fixedControlsContainer = document.createElement('div');
    fixedControlsContainer.className = 'generate-module-fixed-controls';

    // 创建三按钮容器（垂直排列）
    const topButtonsContainer = document.createElement('div');
    topButtonsContainer.className = 'top-buttons-container';

    fixedControlsContainer.appendChild(topButtonsContainer);
    return fixedControlsContainer;
}

/**
 * 创建顶部按钮组
 */
function createTopButtons(
    isPrivilegedUser: boolean,
    moduleNames: string[]
): {
    selectAllItem: HTMLElement;
    generateButton: HTMLElement;
    addButton: HTMLElement | null;
} {


    // 全选按钮
    const selectAllItem = document.createElement('button');
    selectAllItem.className = 'select-all select-all-item module-item-base';
    selectAllItem.textContent = '全选';
    selectAllItem.dataset.action = 'select-all';

    // 生成按钮
    const generateButton = document.createElement('button');
    generateButton.className = 'apply-theme-apply-btn module-item-base';
    generateButton.textContent = '生成';

    // 添加按钮（特权用户可见）
    let addButton: HTMLElement | null = null;
    if (isPrivilegedUser) {
        addButton = document.createElement('button');
        addButton.className = 'add-module-btn module-item-base';
        addButton.textContent = '添加';
    }

    // 设置按钮状态
    setupButtonStates(selectAllItem, moduleNames);

    return { selectAllItem, generateButton, addButton };
}

/**
 * 设置按钮状态
 */
function setupButtonStates(selectAllItem: HTMLElement, moduleNames: string[]): void {
    // 根据模块选择状态更新全选按钮
    const selectedCount = moduleNames.filter(moduleName => moduleStatusModifications[ moduleName ] === true).length;
    if (selectedCount === moduleNames.length) {
        // 全部选中
        selectAllItem.classList.add('selected');
    } else {
        // 部分选中或全部未选中
        selectAllItem.classList.remove('selected');
    }
}

/**
 * 设置按钮事件
 */
function setupButtonEvents(
    addButton: HTMLElement | null,
    dropdownMenu: HTMLElement
): void {
    // 按钮hover效果已移动到CSS中
    // 如果需要特殊的交互效果，可以在这里添加

    // 添加按钮事件（如果存在）
    if (addButton) {
        // 添加按钮点击事件 - 弹窗方式
        addButton.addEventListener('click', () => {
            showAddModuleDialog(dropdownMenu);
        });
    }
}

/**
 * 创建可滚动的模块列表区域
 */
function createScrollableModuleList(moduleNames: string[]): HTMLElement {
    const scrollableModuleList = document.createElement('div');
    scrollableModuleList.className = 'scrollable-module-list';
    // 使用文档片段来减少DOM重排
    const moduleFragment = document.createDocumentFragment();

    // 添加模块选项到文档片段
    moduleNames.forEach((moduleName) => {
        const item = document.createElement('div');
        // 根据moduleStatusModifications中的值设置选中状态
        const isSelected = moduleStatusModifications[ moduleName ] === true;
        item.className = `apply-theme-dropdown-item module-item-base ${isSelected ? 'selected' : ''}`;
        item.dataset.module = moduleName;
        item.textContent = getModuleDisplayName(moduleName);
        moduleFragment.appendChild(item);
    });

    // 将模块列表添加到可滚动区域
    scrollableModuleList.appendChild(moduleFragment);
    return scrollableModuleList;
}



/**
 * 填充模块选择选项
 * @param dropdownMenu 下拉菜单元素
 * @param versionType 版本类型
 */
export async function populateModuleOptions(dropdownMenu: HTMLElement, versionType: 'color' | 'image' | 'full'): Promise<void> {
    try {
        // 使用文档片段来减少DOM重排
        const fragment = document.createDocumentFragment();

        const modules = await getAvailableModules(versionType);

        if (modules.length === 0) {
            dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">暂无可用模块</div>';
            return;
        }

        // 清空菜单内容
        dropdownMenu.innerHTML = '';

        // 添加模块选项到文档片段
        modules.forEach(moduleName => {
            const item = document.createElement('div');
            item.className = 'apply-theme-dropdown-item';
            item.textContent = getModuleDisplayName(moduleName);

            // 点击模块直接应用
            item.addEventListener('click', async () => {
                try {
                    // 动态导入相应的应用函数以避免循环依赖
                    const { applyColorValueTheme, applyImageTheme, applyFullTheme } = await import('./apply_random_theme');
                    const { updateScreenshotAndPreviewArea } = await import('./screenshot_manager');

                    switch (versionType) {
                        case 'color':
                            await applyColorValueTheme([ moduleName ]);
                            break;
                        case 'image':
                            await applyImageTheme([ moduleName ]);
                            break;
                        case 'full':
                            await applyFullTheme([ moduleName ]);
                            break;
                    }

                    // 应用完成后更新截图显示区域和预览区域
                    await updateScreenshotAndPreviewArea();

                    // 应用成功后隐藏菜单
                    dropdownMenu.style.display = 'none';
                    const dropdownButton = dropdownMenu.parentElement?.querySelector('.apply-theme-dropdown-btn') as HTMLElement;
                    if (dropdownButton) {
                        dropdownButton.textContent = '▼';
                        dropdownButton.classList.remove('active');
                    }
                } catch (error) {
                    console.error(`应用模块 ${moduleName} 失败:`, error);
                    messageError(`应用模块 ${moduleName} 失败`);
                }
            });

            fragment.appendChild(item);
        });

        // 一次性添加所有模块到DOM
        dropdownMenu.appendChild(fragment);

        // 下拉菜单样式已在CSS中定义

    } catch (error) {
        console.error('填充模块选项失败:', error);
        dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">加载模块失败</div>';

        // 下拉菜单样式已在CSS中定义
    }
}

/**
 * 显示添加模块对话框
 * @param dropdownMenu 下拉菜单元素
 */
function showAddModuleDialog(dropdownMenu: HTMLElement): void {
    // 创建对话框遮罩
    const overlay = document.createElement('div');
    overlay.className = 'add-module-dialog-overlay';

    // 创建对话框
    const dialog = createAddModuleDialog();
    overlay.appendChild(dialog);

    // 添加到页面
    document.body.appendChild(overlay);

    // 设置对话框事件
    setupAddModuleDialogEvents(overlay, dialog, dropdownMenu);
}

/**
 * 创建添加模块对话框
 */
function createAddModuleDialog(): HTMLElement {
    const dialog = document.createElement('div');
    dialog.className = 'add-module-dialog';

    // 添加动画样式（只添加一次）
    if (!document.querySelector('style[data-dialog-animation]')) {
        const style = document.createElement('style');
        style.setAttribute('data-dialog-animation', 'true');
        style.textContent = `
            @keyframes dialogFadeIn {
                from {
                    opacity: 0;
                    transform: scale(0.95) translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 标题
    const title = document.createElement('h3');
    title.textContent = '添加新模块';

    // 输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.placeholder = '请输入模块名称';

    // 按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'dialog-button-container';

    // 取消按钮
    const cancelButton = document.createElement('button');
    cancelButton.textContent = '取消';
    cancelButton.className = 'cancel-button';

    // 确认按钮
    const confirmButton = document.createElement('button');
    confirmButton.textContent = '添加';
    confirmButton.className = 'confirm-button';

    // 按钮样式已在CSS中定义

    // 组装对话框
    buttonContainer.appendChild(cancelButton);
    buttonContainer.appendChild(confirmButton);
    dialog.appendChild(title);
    dialog.appendChild(input);
    dialog.appendChild(buttonContainer);

    return dialog;
}



/**
 * 设置添加模块对话框事件
 */
function setupAddModuleDialogEvents(
    overlay: HTMLElement,
    dialog: HTMLElement,
    dropdownMenu: HTMLElement
): void {
    const input = dialog.querySelector('input')!;
    const cancelButton = dialog.querySelector('.cancel-button')!;
    const confirmButton = dialog.querySelector('.confirm-button')!;

    // 自动聚焦输入框
    setTimeout(() => input.focus(), 100);

    // 关闭对话框函数
    const closeDialog = () => {
        document.body.removeChild(overlay);
    };

    // 事件监听
    cancelButton.addEventListener('click', closeDialog);

    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            closeDialog();
        }
    });

    // ESC键关闭
    const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            closeDialog();
            document.removeEventListener('keydown', handleKeyDown);
        }
        if (e.key === 'Enter') {
            (confirmButton as HTMLElement).click();
        }
    };
    document.addEventListener('keydown', handleKeyDown);

    // 确认按钮事件
    confirmButton.addEventListener('click', async () => {
        const moduleName = (input as HTMLInputElement).value.trim();
        if (!moduleName) {
            messageError('请输入模块名称');
            return;
        }

        await handleAddModuleToConfig(moduleName, dropdownMenu);
        closeDialog();
        document.removeEventListener('keydown', handleKeyDown);
    });
}

/**
 * 处理添加模块到配置的逻辑
 * @param moduleName 模块名称
 * @param dropdownMenu 下拉菜单元素
 */
async function handleAddModuleToConfig(moduleName: string, dropdownMenu: HTMLElement): Promise<void> {
    try {
        // 检查模块是否已存在
        const existingItems = dropdownMenu.querySelectorAll('.apply-theme-dropdown-item:not(.select-all)');
        const exists = Array.from(existingItems).some(item =>
            (item as HTMLElement).dataset.module === moduleName
        );

        if (exists) {
            messageError('模块已存在');
            return;
        }

        // 将新模块添加到全局变量中，默认值为true
        moduleStatusModifications[ moduleName ] = true;
        markHasModifications();

        console.log(`模块 ${moduleName} 已添加到内存配置`);

        // 创建新的模块项
        const newItem = document.createElement('div');
        newItem.className = 'apply-theme-dropdown-item selected';
        newItem.dataset.module = moduleName;
        newItem.textContent = getModuleDisplayName(moduleName);
        newItem.className = 'apply-theme-dropdown-item module-item-base';

        // 插入到可滚动模块列表中
        const scrollableModuleList = dropdownMenu.querySelector('.scrollable-module-list');
        if (scrollableModuleList) {
            scrollableModuleList.appendChild(newItem);
        }

        // 重新设置事件监听器
        setupNewModuleItemEvents(newItem, dropdownMenu);

        messageSuccess(`模块 ${moduleName} 已添加成功`);

    } catch (error) {
        console.error('添加模块失败:', error);
        messageError(`添加模块失败: ${error}`);
    }
}

/**
 * 为新添加的模块项设置事件监听器
 */
function setupNewModuleItemEvents(newItem: HTMLElement, dropdownMenu: HTMLElement): void {
    // 为新项添加点击事件
    newItem.addEventListener('click', () => {
        newItem.classList.toggle('selected');

        // 更新全选状态
        const moduleItems = dropdownMenu.querySelectorAll('.scrollable-module-list .apply-theme-dropdown-item') as NodeListOf<HTMLElement>;
        const selectAllItem = dropdownMenu.querySelector('.select-all') as HTMLElement;
        const selectedCount = Array.from(moduleItems).filter(item => item.classList.contains('selected')).length;

        if (selectedCount === moduleItems.length) {
            selectAllItem.classList.add('selected');
        } else {
            selectAllItem.classList.remove('selected');
        }

        // 保存选择状态
        saveModuleSelections(moduleItems);
    });
}

/**
 * 保存模块选择状态到全局变量
 * @param moduleItems 模块项目列表
 */
function saveModuleSelections(moduleItems: NodeListOf<HTMLElement>): void {
    moduleItems.forEach(item => {
        const moduleName = item.dataset.module;
        if (moduleName) {
            const isSelected = item.classList.contains('selected');
            moduleStatusModifications[ moduleName ] = isSelected;
            markHasModifications();
        }
    });
}